# 政务大数据共享交换平台 (Government Data Sharing Platform)

这是一个使用C# WPF开发的模拟政务大数据共享交换平台的可视化界面。

## 项目概述

本应用模拟了一个政府部门数据共享交换平台的可视化界面，包含以下主要功能：

- 资源总量构成饼图展示
- 资源总量统计图表
- 基础库统计图表
- 数据共享次数表格和柱状图
- 实时时钟显示

## 技术栈

- C# WPF
- .NET 6.0
- LiveCharts.Wpf (图表库)
- MaterialDesignThemes (UI组件库)

## 运行要求

- Windows操作系统
- .NET 6.0 SDK 或更高版本
- Visual Studio 2019/2022 或其他支持WPF的IDE

## 如何运行

1. 确保已安装.NET 6.0 SDK
2. 使用Visual Studio打开解决方案文件 `DataVisualization.sln`
3. 还原NuGet包
4. 编译并运行项目

## 项目结构

- **App.xaml/App.xaml.cs**: 应用程序入口和全局资源定义
- **MainWindow.xaml/MainWindow.xaml.cs**: 主窗口UI和数据绑定逻辑

## 自定义开发

如需修改UI或添加新功能：

1. 在App.xaml中可以调整颜色和样式
2. 在MainWindow.xaml.cs中可以修改数据模型
3. 在MainWindow.xaml中可以调整布局和UI元素

## 授权

本项目仅供学习和演示使用。 