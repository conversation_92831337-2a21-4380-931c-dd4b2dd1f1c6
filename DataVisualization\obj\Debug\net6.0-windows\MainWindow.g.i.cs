﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C6DB1B52A2D71B0CFE4A33DF13E90F05A10E3680"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using DataVisualization;
using HelixToolkit.Wpf;
using LiveCharts.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DataVisualization {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 56 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeDateDisplay;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.HelixViewport3D FlashlightViewport;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D FlashlightBody;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D FlashlightHead;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D FlashlightLens;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D FlashlightBottom;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.HelixViewport3D MilitaryDeviceViewport;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D MainChassis;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D FrontPanel;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D DisplayScreen;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D Connector1;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D Connector2;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D Connector3;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D Connector4;
        
        #line default
        #line hidden
        
        
        #line 401 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D ControlKnob1;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D ControlKnob2;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D ControlKnob3;
        
        #line default
        #line hidden
        
        
        #line 437 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D PowerButton;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D VentHole1;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D VentHole2;
        
        #line default
        #line hidden
        
        
        #line 473 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D VentHole3;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.HelixViewport3D IndustrialViewport;
        
        #line default
        #line hidden
        
        
        #line 514 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D MainPipe1;
        
        #line default
        #line hidden
        
        
        #line 526 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D MainPipe2;
        
        #line default
        #line hidden
        
        
        #line 538 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D MainPipe3;
        
        #line default
        #line hidden
        
        
        #line 550 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D Filter1;
        
        #line default
        #line hidden
        
        
        #line 562 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D Filter2;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.SphereVisual3D Filter3;
        
        #line default
        #line hidden
        
        
        #line 586 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D Frame1;
        
        #line default
        #line hidden
        
        
        #line 598 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D Frame2;
        
        #line default
        #line hidden
        
        
        #line 610 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HelixToolkit.Wpf.BoxVisual3D BasePlatform;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DataVisualization;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TimeDateDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.FlashlightViewport = ((HelixToolkit.Wpf.HelixViewport3D)(target));
            return;
            case 3:
            this.FlashlightBody = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 4:
            this.FlashlightHead = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 5:
            this.FlashlightLens = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 6:
            this.FlashlightBottom = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 7:
            this.MilitaryDeviceViewport = ((HelixToolkit.Wpf.HelixViewport3D)(target));
            return;
            case 8:
            this.MainChassis = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 9:
            this.FrontPanel = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 10:
            this.DisplayScreen = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 11:
            this.Connector1 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 12:
            this.Connector2 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 13:
            this.Connector3 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 14:
            this.Connector4 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 15:
            this.ControlKnob1 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 16:
            this.ControlKnob2 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 17:
            this.ControlKnob3 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 18:
            this.PowerButton = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 19:
            this.VentHole1 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 20:
            this.VentHole2 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 21:
            this.VentHole3 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 22:
            this.IndustrialViewport = ((HelixToolkit.Wpf.HelixViewport3D)(target));
            return;
            case 23:
            this.MainPipe1 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 24:
            this.MainPipe2 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 25:
            this.MainPipe3 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 26:
            this.Filter1 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 27:
            this.Filter2 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 28:
            this.Filter3 = ((HelixToolkit.Wpf.SphereVisual3D)(target));
            return;
            case 29:
            this.Frame1 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 30:
            this.Frame2 = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            case 31:
            this.BasePlatform = ((HelixToolkit.Wpf.BoxVisual3D)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

