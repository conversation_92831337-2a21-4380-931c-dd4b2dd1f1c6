<Application x:Class="DataVisualization.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DataVisualization"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Dark.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Deep Blue Gradient Background -->
            <LinearGradientBrush x:Key="DeepBlueGradient" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#0B1A30" Offset="0.0"/>
                <GradientStop Color="#102040" Offset="1.0"/>
            </LinearGradientBrush>

            <!-- Border Brush -->
            <SolidColorBrush x:Key="BorderBrush" Color="#1976D2"/>

            <!-- Panel Background Brush -->
            <SolidColorBrush x:Key="PanelBackgroundBrush" Color="#0A1525" Opacity="0.7"/>

            <!-- Text Colors -->
            <SolidColorBrush x:Key="PrimaryTextBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="SecondaryTextBrush" Color="#90CAF9"/>
            <SolidColorBrush x:Key="AccentTextBrush" Color="#29B6F6"/>

            <!-- Chart Colors -->
            <SolidColorBrush x:Key="Chart1" Color="#29B6F6"/>
            <SolidColorBrush x:Key="Chart2" Color="#FF4081"/>
            <SolidColorBrush x:Key="Chart3" Color="#4CAF50"/>
            
            <!-- Pie Chart Colors -->
            <SolidColorBrush x:Key="Pie1" Color="#FF5252"/>
            <SolidColorBrush x:Key="Pie2" Color="#FFAB40"/>
            <SolidColorBrush x:Key="Pie3" Color="#69F0AE"/>
            <SolidColorBrush x:Key="Pie4" Color="#40C4FF"/>
            <SolidColorBrush x:Key="Pie5" Color="#B388FF"/>
            <SolidColorBrush x:Key="Pie6" Color="#EA80FC"/>
            
            <!-- Style for Panel Titles -->
            <Style x:Key="PanelTitleStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
                <Setter Property="Margin" Value="10,5,0,10"/>
            </Style>
            
            <!-- Style for Data Labels -->
            <Style x:Key="DataLabelStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="FontWeight" Value="Bold"/>
            </Style>
            
            <!-- Style for Panels -->
            <Style x:Key="DataPanelStyle" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource PanelBackgroundBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="5"/>
                <Setter Property="Margin" Value="5"/>
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application> 