<Window x:Class="DataVisualization.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DataVisualization"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        mc:Ignorable="d"
        Title="政务大数据共享交换平台" Height="900" Width="1600"
        WindowStartupLocation="CenterScreen" WindowState="Maximized">
    
    <Window.Resources>
        <Style TargetType="TabItem">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="Border" BorderThickness="0" Background="Transparent" Margin="0,0,0,0">
                            <ContentPresenter x:Name="ContentSite" ContentSource="Header" Margin="10,2"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#1976D2"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="False">
                                <Setter Property="Foreground" Value="#90CAF9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid Background="{StaticResource DeepBlueGradient}">
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="20"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" BorderThickness="0,0,0,1" BorderBrush="{StaticResource BorderBrush}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="政务大数据共享交换平台" FontSize="30" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryTextBrush}" 
                         VerticalAlignment="Center" HorizontalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                    <TextBlock x:Name="TimeDateDisplay" Text="13:52:10 2025年05月29日 星期四" 
                             Foreground="{StaticResource PrimaryTextBrush}" 
                             FontSize="16" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Top Row -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 资源总量构成 -->
                <Border Grid.Column="0" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="资源总量构成" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <lvc:PieChart Grid.Row="1" Series="{Binding ResourceCompositionSeries}" 
                                    LegendLocation="Right" Margin="10" 
                                    DataTooltip="{x:Null}" Hoverable="False">
                            <lvc:PieChart.ChartLegend>
                                <lvc:DefaultLegend FontSize="12" Foreground="White"/>
                            </lvc:PieChart.ChartLegend>
                        </lvc:PieChart>
                    </Grid>
                </Border>
                
                <!-- 资源总量统计 -->
                <Border Grid.Column="1" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="资源总量统计" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="15681" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="数据总量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="1731" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="更新量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="11753" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="共享次数" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                        
                        <lvc:CartesianChart Grid.Row="2" Series="{Binding ResourceStatisticsSeries}" 
                                           LegendLocation="Top" Margin="10"
                                           DisableAnimations="True">
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="月份" Labels="{Binding MonthLabels}" Foreground="White"/>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="数量" Foreground="White"/>
                            </lvc:CartesianChart.AxisY>
                            <lvc:CartesianChart.ChartLegend>
                                <lvc:DefaultLegend FontSize="12" Foreground="White"/>
                            </lvc:CartesianChart.ChartLegend>
                        </lvc:CartesianChart>
                    </Grid>
                </Border>
                
                <!-- 基础库统计 -->
                <Border Grid.Column="2" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="基础库统计" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="5481" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="数据总量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="1331" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="更新量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="3753" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="共享次数" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                        
                        <lvc:CartesianChart Grid.Row="2" Series="{Binding BasicDatabaseSeries}" 
                                           LegendLocation="Top" Margin="10"
                                           DisableAnimations="True">
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="月份" Labels="{Binding MonthLabels}" Foreground="White"/>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="数量" Foreground="White"/>
                            </lvc:CartesianChart.AxisY>
                            <lvc:CartesianChart.ChartLegend>
                                <lvc:DefaultLegend FontSize="12" Foreground="White"/>
                            </lvc:CartesianChart.ChartLegend>
                        </lvc:CartesianChart>
                    </Grid>
                </Border>
            </Grid>
            
            <!-- Bottom Row -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 数据共享次数表格 -->
                <Border Grid.Column="0" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="数据共享次数" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <DataGrid Grid.Row="1" ItemsSource="{Binding DataSharingList}" 
                                 AutoGenerateColumns="False" IsReadOnly="True"
                                 Background="Transparent" BorderThickness="0"
                                 GridLinesVisibility="All"
                                 RowBackground="Transparent"
                                 AlternatingRowBackground="#102040"
                                 VerticalGridLinesBrush="#1976D2"
                                 HorizontalGridLinesBrush="#1976D2"
                                 Foreground="White">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="资源名称" Binding="{Binding ResourceName}" Width="*"/>
                                <DataGridTextColumn Header="调用方" Binding="{Binding CallerName}" Width="*"/>
                                <DataGridTextColumn Header="调用时间" Binding="{Binding CallTime}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
                
                <!-- 数据共享次数柱状图 -->
                <Border Grid.Column="1" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="数据共享次数" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <lvc:CartesianChart Grid.Row="1" Series="{Binding SharingCountSeries}" 
                                          LegendLocation="None" Margin="10"
                                          DisableAnimations="True">
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="月份" Labels="{Binding MonthLabels}" Foreground="White"/>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="共享次数" Foreground="White" MinValue="0"/>
                            </lvc:CartesianChart.AxisY>
                        </lvc:CartesianChart>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
        
        <!-- Bottom Status Bar -->
        <Border Grid.Row="2" BorderThickness="0,1,0,0" BorderBrush="{StaticResource BorderBrush}">
            <StatusBar Background="Transparent">
                <StatusBarItem>
                    <TextBlock Text="© 2025 政务大数据共享交换平台" Foreground="{StaticResource SecondaryTextBrush}" FontSize="11"/>
                </StatusBarItem>
            </StatusBar>
        </Border>
        
        <!-- Tab Panel at the Bottom of the Top Row (Above Lower Panel) -->
        <TabControl Grid.Row="1" Background="Transparent" BorderThickness="0" Margin="10,280,10,0"
                   VerticalAlignment="Top" Height="30">
            <TabItem Header="法人库" Foreground="White"/>
            <TabItem Header="人口库" Foreground="White"/>
            <TabItem Header="电子证照库" Foreground="White"/>
        </TabControl>
    </Grid>
</Window> 