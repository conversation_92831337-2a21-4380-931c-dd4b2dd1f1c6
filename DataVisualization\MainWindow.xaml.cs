using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Timers;
using System.Windows;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Wpf;

namespace DataVisualization
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private Timer _timer;

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;
            
            // Initialize timer for updating the time display
            _timer = new Timer(1000);
            _timer.Elapsed += Timer_Elapsed;
            _timer.Start();
            
            // Initialize data
            InitializeData();
        }

        private void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            // Update time display on UI thread
            Dispatcher.Invoke(() =>
            {
                DateTime now = DateTime.Now;
                string dayOfWeek = "";
                
                // Get Chinese day of week
                switch (now.DayOfWeek)
                {
                    case DayOfWeek.Monday: dayOfWeek = "星期一"; break;
                    case DayOfWeek.Tuesday: dayOfWeek = "星期二"; break;
                    case DayOfWeek.Wednesday: dayOfWeek = "星期三"; break;
                    case DayOfWeek.Thursday: dayOfWeek = "星期四"; break;
                    case DayOfWeek.Friday: dayOfWeek = "星期五"; break;
                    case DayOfWeek.Saturday: dayOfWeek = "星期六"; break;
                    case DayOfWeek.Sunday: dayOfWeek = "星期日"; break;
                }
                
                TimeDateDisplay.Text = $"{now:HH:mm:ss} {now:yyyy年MM月dd日} {dayOfWeek}";
            });
        }

        #region Chart Data Properties

        public SeriesCollection ResourceCompositionSeries { get; set; }
        public SeriesCollection ResourceStatisticsSeries { get; set; }
        public SeriesCollection BasicDatabaseSeries { get; set; }
        public SeriesCollection SharingCountSeries { get; set; }
        public string[] MonthLabels { get; set; }
        public ObservableCollection<DataSharingEntry> DataSharingList { get; set; }

        #endregion

        private void InitializeData()
        {
            // Initialize month labels (1月 to 9月)
            MonthLabels = new[] { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月" };

            // Initialize Resource Composition Pie Chart
            ResourceCompositionSeries = new SeriesCollection
            {
                new PieSeries
                {
                    Title = "公安局：435 (8.39%)",
                    Values = new ChartValues<double> { 435 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie1")
                },
                new PieSeries
                {
                    Title = "民政局：679 (13.1%)",
                    Values = new ChartValues<double> { 679 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie2")
                },
                new PieSeries
                {
                    Title = "气象局：848 (16.35%)",
                    Values = new ChartValues<double> { 848 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie3")
                },
                new PieSeries
                {
                    Title = "统计局：348 (6.71%)",
                    Values = new ChartValues<double> { 348 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie4")
                },
                new PieSeries
                {
                    Title = "交通局：679 (13.1%)",
                    Values = new ChartValues<double> { 679 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie5")
                },
                new PieSeries
                {
                    Title = "人社局：848 (16.35%)",
                    Values = new ChartValues<double> { 848 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie6")
                },
                new PieSeries
                {
                    Title = "其他：1348 (26%)",
                    Values = new ChartValues<double> { 1348 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie1")
                }
            };

            // Initialize Resource Statistics Chart
            ResourceStatisticsSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "数据总量",
                    Values = new ChartValues<double> { 3000, 2800, 3200, 3500, 3000, 3400, 2900, 3100, 3300 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart3"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 76, 175, 80))
                },
                new LineSeries
                {
                    Title = "共享次数",
                    Values = new ChartValues<double> { 1800, 1900, 1750, 1850, 1900, 1800, 1950, 2000, 1950 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart2"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 255, 64, 129))
                },
                new LineSeries
                {
                    Title = "更新量",
                    Values = new ChartValues<double> { 500, 550, 500, 600, 500, 550, 650, 600, 550 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart1"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 41, 182, 246))
                }
            };

            // Initialize Basic Database Chart
            BasicDatabaseSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "数据总量",
                    Values = new ChartValues<double> { 800, 900, 500, 700, 950, 800, 750, 900, 850 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart3"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 76, 175, 80))
                },
                new LineSeries
                {
                    Title = "共享次数",
                    Values = new ChartValues<double> { 750, 900, 200, 500, 350, 800, 250, 550, 500 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart2"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 255, 64, 129))
                },
                new LineSeries
                {
                    Title = "更新量",
                    Values = new ChartValues<double> { 150, 180, 150, 120, 170, 140, 160, 130, 120 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart1"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 41, 182, 246))
                }
            };

            // Initialize Sharing Count Chart (Column Chart)
            SharingCountSeries = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = "资源总量",
                    Values = new ChartValues<double> { 1243, 2315, 1169, 3021, 3521, 4121, 3001, 1983, 1432 },
                    Fill = (Brush)FindResource("Chart3"),
                    MaxColumnWidth = 80
                }
            };

            // Initialize Data Sharing List
            DataSharingList = new ObservableCollection<DataSharingEntry>
            {
                new DataSharingEntry { ResourceName = "残疾人鉴定数据", CallerName = "残联", CallTime = "08:20:36" },
                new DataSharingEntry { ResourceName = "委托下级土要申诉", CallerName = "乡下部门", CallTime = "08:20:46" },
                new DataSharingEntry { ResourceName = "公务员土要职责", CallerName = "公安局", CallTime = "08:20:56" },
                new DataSharingEntry { ResourceName = "更名精美大师", CallerName = "友好公司", CallTime = "DreamCoders" },
                new DataSharingEntry { ResourceName = "交通运输土要职责", CallerName = "完全免费分享", CallTime = "08:21:07" }
            };
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class DataSharingEntry
    {
        public string ResourceName { get; set; }
        public string CallerName { get; set; }
        public string CallTime { get; set; }
    }
} 