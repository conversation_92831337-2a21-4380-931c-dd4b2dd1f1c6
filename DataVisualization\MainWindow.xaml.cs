using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Timers;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Media3D;
using System.Windows.Input;
using LiveCharts;
using LiveCharts.Wpf;
using HelixToolkit.Wpf;

namespace DataVisualization
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private Timer _timer;

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;

            // Initialize timer for updating the time display
            _timer = new Timer(1000);
            _timer.Elapsed += Timer_Elapsed;
            _timer.Start();

            // Initialize data
            InitializeData();

            // Initialize 3D flashlight
            Initialize3DFlashlight();

            // Initialize 3D industrial equipment
            Initialize3DIndustrialEquipment();
        }

        private void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            // Update time display on UI thread
            Dispatcher.Invoke(() =>
            {
                DateTime now = DateTime.Now;
                string dayOfWeek = "";
                
                // Get Chinese day of week
                switch (now.DayOfWeek)
                {
                    case DayOfWeek.Monday: dayOfWeek = "星期一"; break;
                    case DayOfWeek.Tuesday: dayOfWeek = "星期二"; break;
                    case DayOfWeek.Wednesday: dayOfWeek = "星期三"; break;
                    case DayOfWeek.Thursday: dayOfWeek = "星期四"; break;
                    case DayOfWeek.Friday: dayOfWeek = "星期五"; break;
                    case DayOfWeek.Saturday: dayOfWeek = "星期六"; break;
                    case DayOfWeek.Sunday: dayOfWeek = "星期日"; break;
                }
                
                TimeDateDisplay.Text = $"{now:HH:mm:ss} {now:yyyy年MM月dd日} {dayOfWeek}";
            });
        }

        #region Chart Data Properties

        public SeriesCollection ResourceCompositionSeries { get; set; }
        public SeriesCollection ResourceStatisticsSeries { get; set; }
        public SeriesCollection BasicDatabaseSeries { get; set; }
        public SeriesCollection SharingCountSeries { get; set; }
        public string[] MonthLabels { get; set; }
        public ObservableCollection<DataSharingEntry> DataSharingList { get; set; }

        #endregion

        private void InitializeData()
        {
            // Initialize month labels (1月 to 9月)
            MonthLabels = new[] { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月" };

            // Initialize Resource Composition Pie Chart
            ResourceCompositionSeries = new SeriesCollection
            {
                new PieSeries
                {
                    Title = "公安局：435 (8.39%)",
                    Values = new ChartValues<double> { 435 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie1")
                },
                new PieSeries
                {
                    Title = "民政局：679 (13.1%)",
                    Values = new ChartValues<double> { 679 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie2")
                },
                new PieSeries
                {
                    Title = "气象局：848 (16.35%)",
                    Values = new ChartValues<double> { 848 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie3")
                },
                new PieSeries
                {
                    Title = "统计局：348 (6.71%)",
                    Values = new ChartValues<double> { 348 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie4")
                },
                new PieSeries
                {
                    Title = "交通局：679 (13.1%)",
                    Values = new ChartValues<double> { 679 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie5")
                },
                new PieSeries
                {
                    Title = "人社局：848 (16.35%)",
                    Values = new ChartValues<double> { 848 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie6")
                },
                new PieSeries
                {
                    Title = "其他：1348 (26%)",
                    Values = new ChartValues<double> { 1348 },
                    DataLabels = true,
                    LabelPoint = point => "",
                    Fill = (Brush)FindResource("Pie1")
                }
            };

            // Initialize Resource Statistics Chart
            ResourceStatisticsSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "数据总量",
                    Values = new ChartValues<double> { 3000, 2800, 3200, 3500, 3000, 3400, 2900, 3100, 3300 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart3"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 76, 175, 80))
                },
                new LineSeries
                {
                    Title = "共享次数",
                    Values = new ChartValues<double> { 1800, 1900, 1750, 1850, 1900, 1800, 1950, 2000, 1950 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart2"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 255, 64, 129))
                },
                new LineSeries
                {
                    Title = "更新量",
                    Values = new ChartValues<double> { 500, 550, 500, 600, 500, 550, 650, 600, 550 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart1"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 41, 182, 246))
                }
            };

            // Initialize Basic Database Chart
            BasicDatabaseSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "数据总量",
                    Values = new ChartValues<double> { 800, 900, 500, 700, 950, 800, 750, 900, 850 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart3"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 76, 175, 80))
                },
                new LineSeries
                {
                    Title = "共享次数",
                    Values = new ChartValues<double> { 750, 900, 200, 500, 350, 800, 250, 550, 500 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart2"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 255, 64, 129))
                },
                new LineSeries
                {
                    Title = "更新量",
                    Values = new ChartValues<double> { 150, 180, 150, 120, 170, 140, 160, 130, 120 },
                    PointGeometry = null,
                    LineSmoothness = 1,
                    Stroke = (Brush)FindResource("Chart1"),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 41, 182, 246))
                }
            };

            // Initialize Sharing Count Chart (Column Chart)
            SharingCountSeries = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = "资源总量",
                    Values = new ChartValues<double> { 1243, 2315, 1169, 3021, 3521, 4121, 3001, 1983, 1432 },
                    Fill = (Brush)FindResource("Chart3"),
                    MaxColumnWidth = 80
                }
            };

            // Initialize Data Sharing List
            DataSharingList = new ObservableCollection<DataSharingEntry>
            {
                new DataSharingEntry { ResourceName = "残疾人鉴定数据", CallerName = "残联", CallTime = "08:20:36" },
                new DataSharingEntry { ResourceName = "委托下级土要申诉", CallerName = "乡下部门", CallTime = "08:20:46" },
                new DataSharingEntry { ResourceName = "公务员土要职责", CallerName = "公安局", CallTime = "08:20:56" },
                new DataSharingEntry { ResourceName = "更名精美大师", CallerName = "友好公司", CallTime = "DreamCoders" },
                new DataSharingEntry { ResourceName = "交通运输土要职责", CallerName = "完全免费分享", CallTime = "08:21:07" }
            };
        }

        /// <summary>
        /// 初始化3D手电筒
        /// </summary>
        private void Initialize3DFlashlight()
        {
            // 设置3D视口的鼠标交互
            FlashlightViewport.MouseDown += FlashlightViewport_MouseDown;
            FlashlightViewport.MouseMove += FlashlightViewport_MouseMove;
            FlashlightViewport.MouseUp += FlashlightViewport_MouseUp;
            FlashlightViewport.MouseWheel += FlashlightViewport_MouseWheel;

            // 启用旋转和缩放手势
            FlashlightViewport.RotateGesture = new MouseGesture(MouseAction.LeftClick);
            FlashlightViewport.PanGesture = new MouseGesture(MouseAction.RightClick);
            FlashlightViewport.ZoomGesture = new MouseGesture(MouseAction.MiddleClick);
        }

        private bool _isMouseDown = false;
        private Point _lastMousePosition;

        /// <summary>
        /// 鼠标按下事件 - 开始拖拽
        /// </summary>
        private void FlashlightViewport_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                _isMouseDown = true;
                _lastMousePosition = e.GetPosition(FlashlightViewport);
                FlashlightViewport.CaptureMouse();
            }
        }

        /// <summary>
        /// 鼠标移动事件 - 旋转手电筒
        /// </summary>
        private void FlashlightViewport_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isMouseDown && e.LeftButton == MouseButtonState.Pressed)
            {
                Point currentPosition = e.GetPosition(FlashlightViewport);
                double deltaX = currentPosition.X - _lastMousePosition.X;
                double deltaY = currentPosition.Y - _lastMousePosition.Y;

                // 创建旋转变换
                var rotationX = new AxisAngleRotation3D(new Vector3D(1, 0, 0), -deltaY * 0.5);
                var rotationY = new AxisAngleRotation3D(new Vector3D(0, 1, 0), deltaX * 0.5);

                // 应用旋转到手电筒组件
                ApplyRotationToFlashlight(rotationX, rotationY);

                _lastMousePosition = currentPosition;
            }
        }

        /// <summary>
        /// 鼠标释放事件 - 结束拖拽
        /// </summary>
        private void FlashlightViewport_MouseUp(object sender, MouseButtonEventArgs e)
        {
            if (_isMouseDown)
            {
                _isMouseDown = false;
                FlashlightViewport.ReleaseMouseCapture();
            }
        }

        /// <summary>
        /// 鼠标滚轮事件 - 缩放
        /// </summary>
        private void FlashlightViewport_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            var camera = FlashlightViewport.Camera as PerspectiveCamera;
            if (camera != null)
            {
                double scaleFactor = e.Delta > 0 ? 0.9 : 1.1;
                var newPosition = new Point3D(
                    camera.Position.X * scaleFactor,
                    camera.Position.Y * scaleFactor,
                    camera.Position.Z * scaleFactor);

                camera.Position = newPosition;
            }
        }

        /// <summary>
        /// 应用旋转变换到手电筒组件
        /// </summary>
        private void ApplyRotationToFlashlight(AxisAngleRotation3D rotationX, AxisAngleRotation3D rotationY)
        {
            var transformGroup = new Transform3DGroup();

            // 获取现有的变换
            if (FlashlightBody.Transform != null)
                transformGroup.Children.Add(FlashlightBody.Transform);

            // 添加新的旋转变换
            transformGroup.Children.Add(new RotateTransform3D(rotationX));
            transformGroup.Children.Add(new RotateTransform3D(rotationY));

            // 应用到所有手电筒组件
            FlashlightBody.Transform = transformGroup.Clone();
            FlashlightHead.Transform = transformGroup.Clone();
            FlashlightLens.Transform = transformGroup.Clone();
            FlashlightBottom.Transform = transformGroup.Clone();
        }

        #region 3D工业设备交互功能

        /// <summary>
        /// 初始化3D工业设备
        /// </summary>
        private void Initialize3DIndustrialEquipment()
        {
            // 设置3D视口的鼠标交互
            IndustrialViewport.MouseDown += IndustrialViewport_MouseDown;
            IndustrialViewport.MouseMove += IndustrialViewport_MouseMove;
            IndustrialViewport.MouseUp += IndustrialViewport_MouseUp;
            IndustrialViewport.MouseWheel += IndustrialViewport_MouseWheel;

            // 启用旋转和缩放手势
            IndustrialViewport.RotateGesture = new MouseGesture(MouseAction.LeftClick);
            IndustrialViewport.PanGesture = new MouseGesture(MouseAction.RightClick);
            IndustrialViewport.ZoomGesture = new MouseGesture(MouseAction.MiddleClick);
        }

        private bool _isIndustrialMouseDown = false;
        private Point _lastIndustrialMousePosition;

        /// <summary>
        /// 工业设备鼠标按下事件
        /// </summary>
        private void IndustrialViewport_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                _isIndustrialMouseDown = true;
                _lastIndustrialMousePosition = e.GetPosition(IndustrialViewport);
                IndustrialViewport.CaptureMouse();
            }
        }

        /// <summary>
        /// 工业设备鼠标移动事件 - 旋转设备
        /// </summary>
        private void IndustrialViewport_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isIndustrialMouseDown && e.LeftButton == MouseButtonState.Pressed)
            {
                Point currentPosition = e.GetPosition(IndustrialViewport);
                double deltaX = currentPosition.X - _lastIndustrialMousePosition.X;
                double deltaY = currentPosition.Y - _lastIndustrialMousePosition.Y;

                // 创建旋转变换
                var rotationX = new AxisAngleRotation3D(new Vector3D(1, 0, 0), -deltaY * 0.3);
                var rotationY = new AxisAngleRotation3D(new Vector3D(0, 1, 0), deltaX * 0.3);

                // 应用旋转到工业设备组件
                ApplyRotationToIndustrialEquipment(rotationX, rotationY);

                _lastIndustrialMousePosition = currentPosition;
            }
        }

        /// <summary>
        /// 工业设备鼠标释放事件
        /// </summary>
        private void IndustrialViewport_MouseUp(object sender, MouseButtonEventArgs e)
        {
            if (_isIndustrialMouseDown)
            {
                _isIndustrialMouseDown = false;
                IndustrialViewport.ReleaseMouseCapture();
            }
        }

        /// <summary>
        /// 工业设备鼠标滚轮事件 - 缩放
        /// </summary>
        private void IndustrialViewport_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            var camera = IndustrialViewport.Camera as PerspectiveCamera;
            if (camera != null)
            {
                double scaleFactor = e.Delta > 0 ? 0.9 : 1.1;
                var newPosition = new Point3D(
                    camera.Position.X * scaleFactor,
                    camera.Position.Y * scaleFactor,
                    camera.Position.Z * scaleFactor);

                camera.Position = newPosition;
            }
        }

        /// <summary>
        /// 应用旋转变换到工业设备组件
        /// </summary>
        private void ApplyRotationToIndustrialEquipment(AxisAngleRotation3D rotationX, AxisAngleRotation3D rotationY)
        {
            var transformGroup = new Transform3DGroup();

            // 获取现有的变换
            if (MainPipe1.Transform != null)
                transformGroup.Children.Add(MainPipe1.Transform);

            // 添加新的旋转变换
            transformGroup.Children.Add(new RotateTransform3D(rotationX));
            transformGroup.Children.Add(new RotateTransform3D(rotationY));

            // 应用到所有工业设备组件
            var clonedTransform = transformGroup.Clone();

            // 主管道
            MainPipe1.Transform = clonedTransform.Clone();
            MainPipe2.Transform = clonedTransform.Clone();
            MainPipe3.Transform = clonedTransform.Clone();

            // 过滤器
            Filter1.Transform = clonedTransform.Clone();
            Filter2.Transform = clonedTransform.Clone();
            Filter3.Transform = clonedTransform.Clone();

            // 支撑框架
            Frame1.Transform = clonedTransform.Clone();
            Frame2.Transform = clonedTransform.Clone();

            // 基座平台
            BasePlatform.Transform = clonedTransform.Clone();
        }

        #endregion

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class DataSharingEntry
    {
        public string ResourceName { get; set; }
        public string CallerName { get; set; }
        public string CallTime { get; set; }
    }
}