{"format": 1, "restore": {"D:\\source\\datavisual\\datadisp\\DataVisualization\\DataVisualization\\DataVisualization.csproj": {}}, "projects": {"D:\\source\\datavisual\\datadisp\\DataVisualization\\DataVisualization\\DataVisualization.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\source\\datavisual\\datadisp\\DataVisualization\\DataVisualization\\DataVisualization.csproj", "projectName": "DataVisualization", "projectPath": "D:\\source\\datavisual\\datadisp\\DataVisualization\\DataVisualization\\DataVisualization.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\source\\datavisual\\datadisp\\DataVisualization\\DataVisualization\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"HelixToolkit.Wpf": {"target": "Package", "version": "[2.27.0, )"}, "LiveCharts.Wpf": {"target": "Package", "version": "[0.9.7, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}