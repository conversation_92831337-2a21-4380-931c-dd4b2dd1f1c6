{"version": 2, "dgSpecHash": "xLZ3UHY6JCk=", "success": true, "projectFilePath": "D:\\source\\datavisual\\datadisp\\DataVisualization\\DataVisualization\\DataVisualization.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\helixtoolkit.wpf\\2.27.0\\helixtoolkit.wpf.2.27.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\livecharts\\0.9.7\\livecharts.0.9.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\livecharts.wpf\\0.9.7\\livecharts.wpf.0.9.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesigncolors\\2.1.4\\materialdesigncolors.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes\\4.9.0\\materialdesignthemes.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.39\\microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net6.0-windows7.0”还原包“HelixToolkit.Wpf 2.27.0”。此包可能与项目不完全兼容。", "libraryId": "HelixToolkit.Wpf", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net6.0-windows7.0”还原包“LiveCharts 0.9.7”。此包可能与项目不完全兼容。", "libraryId": "LiveCharts", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net6.0-windows7.0”还原包“LiveCharts.Wpf 0.9.7”。此包可能与项目不完全兼容。", "libraryId": "LiveCharts.Wpf", "targetGraphs": ["net6.0-windows7.0"]}]}