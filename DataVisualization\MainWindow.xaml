<Window x:Class="DataVisualization.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DataVisualization"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        xmlns:helix="http://helix-toolkit.org/wpf"
        mc:Ignorable="d"
        Title="政务大数据共享交换平台" Height="900" Width="1600"
        WindowStartupLocation="CenterScreen" WindowState="Maximized">
    
    <Window.Resources>
        <Style TargetType="TabItem">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="Border" BorderThickness="0" Background="Transparent" Margin="0,0,0,0">
                            <ContentPresenter x:Name="ContentSite" ContentSource="Header" Margin="10,2"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#1976D2"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="False">
                                <Setter Property="Foreground" Value="#90CAF9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid Background="{StaticResource DeepBlueGradient}">
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="20"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" BorderThickness="0,0,0,1" BorderBrush="{StaticResource BorderBrush}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="政务大数据共享交换平台" FontSize="30" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryTextBrush}" 
                         VerticalAlignment="Center" HorizontalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                    <TextBlock x:Name="TimeDateDisplay" Text="13:52:10 2025年05月29日 星期四" 
                             Foreground="{StaticResource PrimaryTextBrush}" 
                             FontSize="16" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Top Row -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 资源总量构成 -->
                <Border Grid.Column="0" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="资源总量构成" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <lvc:PieChart Grid.Row="1" Series="{Binding ResourceCompositionSeries}" 
                                    LegendLocation="Right" Margin="10" 
                                    DataTooltip="{x:Null}" Hoverable="False">
                            <lvc:PieChart.ChartLegend>
                                <lvc:DefaultLegend FontSize="12" Foreground="White"/>
                            </lvc:PieChart.ChartLegend>
                        </lvc:PieChart>
                    </Grid>
                </Border>
                
                <!-- 资源总量统计 -->
                <Border Grid.Column="1" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="资源总量统计" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="15681" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="数据总量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="1731" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="更新量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="11753" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="共享次数" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                        
                        <lvc:CartesianChart Grid.Row="2" Series="{Binding ResourceStatisticsSeries}" 
                                           LegendLocation="Top" Margin="10"
                                           DisableAnimations="True">
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="月份" Labels="{Binding MonthLabels}" Foreground="White"/>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="数量" Foreground="White"/>
                            </lvc:CartesianChart.AxisY>
                            <lvc:CartesianChart.ChartLegend>
                                <lvc:DefaultLegend FontSize="12" Foreground="White"/>
                            </lvc:CartesianChart.ChartLegend>
                        </lvc:CartesianChart>
                    </Grid>
                </Border>
                
                <!-- 基础库统计 -->
                <Border Grid.Column="2" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Text="基础库统计" Style="{StaticResource PanelTitleStyle}"/>
                        
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="5481" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="数据总量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="1331" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="更新量" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="3753" Style="{StaticResource DataLabelStyle}" FontSize="22"/>
                                <TextBlock Text="共享次数" Foreground="{StaticResource AccentTextBrush}" 
                                         HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                        
                        <lvc:CartesianChart Grid.Row="2" Series="{Binding BasicDatabaseSeries}" 
                                           LegendLocation="Top" Margin="10"
                                           DisableAnimations="True">
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="月份" Labels="{Binding MonthLabels}" Foreground="White"/>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="数量" Foreground="White"/>
                            </lvc:CartesianChart.AxisY>
                            <lvc:CartesianChart.ChartLegend>
                                <lvc:DefaultLegend FontSize="12" Foreground="White"/>
                            </lvc:CartesianChart.ChartLegend>
                        </lvc:CartesianChart>
                    </Grid>
                </Border>

                <!-- 3D手电筒展示 -->
                <Border Grid.Column="3" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Text="3D手电筒展示" Style="{StaticResource PanelTitleStyle}"/>

                        <helix:HelixViewport3D Grid.Row="1" x:Name="FlashlightViewport"
                                             Background="Transparent"
                                             ShowCoordinateSystem="False"
                                             ShowViewCube="False"
                                             ZoomExtentsWhenLoaded="True"
                                             Margin="5">

                            <!-- 相机设置 -->
                            <helix:HelixViewport3D.Camera>
                                <PerspectiveCamera Position="5,5,5" LookDirection="-1,-1,-1" UpDirection="0,1,0" FieldOfView="45"/>
                            </helix:HelixViewport3D.Camera>

                            <!-- 光源 -->
                            <helix:DefaultLights/>

                            <!-- 手电筒主体 - 使用BoxVisual3D创建圆柱体效果 -->
                            <helix:BoxVisual3D x:Name="FlashlightBody"
                                             Center="0,0,1.5"
                                             Length="2" Width="2" Height="3">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF404040"/>
                                        <SpecularMaterial Brush="#FF808080" SpecularPower="100"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 手电筒头部 -->
                            <helix:BoxVisual3D x:Name="FlashlightHead"
                                             Center="0,0,3.25"
                                             Length="2.4" Width="2.4" Height="0.5">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF606060"/>
                                        <SpecularMaterial Brush="#FFA0A0A0" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 手电筒镜片 -->
                            <helix:SphereVisual3D x:Name="FlashlightLens"
                                                Center="0,0,3.6" Radius="0.9"
                                                ThetaDiv="20" PhiDiv="20">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#88FFFF00"/>
                                        <SpecularMaterial Brush="#FFFFFF00" SpecularPower="200"/>
                                        <EmissiveMaterial Brush="#44FFFF00"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 手电筒底部 -->
                            <helix:SphereVisual3D x:Name="FlashlightBottom"
                                                Center="0,0,-0.3" Radius="0.8"
                                                ThetaDiv="20" PhiDiv="20">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF303030"/>
                                        <SpecularMaterial Brush="#FF606060" SpecularPower="80"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                        </helix:HelixViewport3D>
                    </Grid>
                </Border>
            </Grid>
            
            <!-- Bottom Row -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 3D军用电子设备 -->
                <Border Grid.Column="0" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Text="3D军用电子设备" Style="{StaticResource PanelTitleStyle}"/>

                        <helix:HelixViewport3D Grid.Row="1" x:Name="MilitaryDeviceViewport"
                                             Background="Transparent"
                                             ShowCoordinateSystem="False"
                                             ShowViewCube="False"
                                             ZoomExtentsWhenLoaded="True"
                                             Margin="5">

                            <!-- 相机设置 -->
                            <helix:HelixViewport3D.Camera>
                                <PerspectiveCamera Position="6,4,8" LookDirection="-1,-0.3,-1" UpDirection="0,1,0" FieldOfView="45"/>
                            </helix:HelixViewport3D.Camera>

                            <!-- 光源 -->
                            <helix:DefaultLights/>

                            <!-- 主机箱体 -->
                            <helix:BoxVisual3D x:Name="MainChassis"
                                             Center="0,0,0"
                                             Length="6" Width="4" Height="1.2">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF2F4F2F"/>
                                        <SpecularMaterial Brush="#FF4F6F4F" SpecularPower="80"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 前面板 -->
                            <helix:BoxVisual3D x:Name="FrontPanel"
                                             Center="0,2.1,0.1"
                                             Length="5.8" Width="0.2" Height="1">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF1F3F1F"/>
                                        <SpecularMaterial Brush="#FF3F5F3F" SpecularPower="90"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 显示屏 -->
                            <helix:BoxVisual3D x:Name="DisplayScreen"
                                             Center="1.5,2.15,0.2"
                                             Length="2" Width="0.1" Height="0.6">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF000000"/>
                                        <SpecularMaterial Brush="#FF333333" SpecularPower="200"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 连接器1 -->
                            <helix:SphereVisual3D x:Name="Connector1"
                                                Center="-2,2.15,0.3" Radius="0.15"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFFFD700"/>
                                        <SpecularMaterial Brush="#FFFFFF80" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 连接器2 -->
                            <helix:SphereVisual3D x:Name="Connector2"
                                                Center="-1.5,2.15,0.3" Radius="0.15"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFFFD700"/>
                                        <SpecularMaterial Brush="#FFFFFF80" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 连接器3 -->
                            <helix:SphereVisual3D x:Name="Connector3"
                                                Center="-2,-2.15,0.3" Radius="0.15"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFFFD700"/>
                                        <SpecularMaterial Brush="#FFFFFF80" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 连接器4 -->
                            <helix:SphereVisual3D x:Name="Connector4"
                                                Center="-1.5,-2.15,0.3" Radius="0.15"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFFFD700"/>
                                        <SpecularMaterial Brush="#FFFFFF80" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 控制旋钮1 -->
                            <helix:SphereVisual3D x:Name="ControlKnob1"
                                                Center="0,2.15,0.25" Radius="0.12"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF000000"/>
                                        <SpecularMaterial Brush="#FF404040" SpecularPower="120"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 控制旋钮2 -->
                            <helix:SphereVisual3D x:Name="ControlKnob2"
                                                Center="0.5,2.15,0.25" Radius="0.12"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF000000"/>
                                        <SpecularMaterial Brush="#FF404040" SpecularPower="120"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 控制旋钮3 -->
                            <helix:SphereVisual3D x:Name="ControlKnob3"
                                                Center="-0.5,2.15,0.25" Radius="0.12"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF000000"/>
                                        <SpecularMaterial Brush="#FF404040" SpecularPower="120"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 电源按钮 -->
                            <helix:SphereVisual3D x:Name="PowerButton"
                                                Center="2.5,2.15,0.25" Radius="0.1"
                                                ThetaDiv="12" PhiDiv="12">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF808080"/>
                                        <SpecularMaterial Brush="#FFC0C0C0" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 散热孔1 -->
                            <helix:BoxVisual3D x:Name="VentHole1"
                                             Center="2.5,0,0.7"
                                             Length="0.1" Width="3" Height="0.1">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF000000"/>
                                        <SpecularMaterial Brush="#FF202020" SpecularPower="50"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 散热孔2 -->
                            <helix:BoxVisual3D x:Name="VentHole2"
                                             Center="2.2,0,0.7"
                                             Length="0.1" Width="3" Height="0.1">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF000000"/>
                                        <SpecularMaterial Brush="#FF202020" SpecularPower="50"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 散热孔3 -->
                            <helix:BoxVisual3D x:Name="VentHole3"
                                             Center="1.9,0,0.7"
                                             Length="0.1" Width="3" Height="0.1">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF000000"/>
                                        <SpecularMaterial Brush="#FF202020" SpecularPower="50"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                        </helix:HelixViewport3D>
                    </Grid>
                </Border>
                
                <!-- 3D工业管道设备展示 -->
                <Border Grid.Column="1" Style="{StaticResource DataPanelStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Text="3D工业管道设备" Style="{StaticResource PanelTitleStyle}"/>

                        <helix:HelixViewport3D Grid.Row="1" x:Name="IndustrialViewport"
                                             Background="Transparent"
                                             ShowCoordinateSystem="False"
                                             ShowViewCube="False"
                                             ZoomExtentsWhenLoaded="True"
                                             Margin="5">

                            <!-- 相机设置 -->
                            <helix:HelixViewport3D.Camera>
                                <PerspectiveCamera Position="8,6,10" LookDirection="-1,-0.5,-1" UpDirection="0,1,0" FieldOfView="50"/>
                            </helix:HelixViewport3D.Camera>

                            <!-- 光源 -->
                            <helix:DefaultLights/>

                            <!-- 主管道1 -->
                            <helix:BoxVisual3D x:Name="MainPipe1"
                                             Center="0,0,0"
                                             Length="6" Width="1.2" Height="1.2">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFE0E0E0"/>
                                        <SpecularMaterial Brush="#FFFFFFFF" SpecularPower="120"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 主管道2 -->
                            <helix:BoxVisual3D x:Name="MainPipe2"
                                             Center="0,1.8,0"
                                             Length="6" Width="1.2" Height="1.2">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFE0E0E0"/>
                                        <SpecularMaterial Brush="#FFFFFFFF" SpecularPower="120"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 主管道3 -->
                            <helix:BoxVisual3D x:Name="MainPipe3"
                                             Center="0,3.6,0"
                                             Length="6" Width="1.2" Height="1.2">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFE0E0E0"/>
                                        <SpecularMaterial Brush="#FFFFFFFF" SpecularPower="120"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 过滤器1 -->
                            <helix:SphereVisual3D x:Name="Filter1"
                                                Center="-2.5,0,0" Radius="1.0"
                                                ThetaDiv="16" PhiDiv="16">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF20B2AA"/>
                                        <SpecularMaterial Brush="#FF40E0D0" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 过滤器2 -->
                            <helix:SphereVisual3D x:Name="Filter2"
                                                Center="-2.5,1.8,0" Radius="1.0"
                                                ThetaDiv="16" PhiDiv="16">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF20B2AA"/>
                                        <SpecularMaterial Brush="#FF40E0D0" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 过滤器3 -->
                            <helix:SphereVisual3D x:Name="Filter3"
                                                Center="-2.5,3.6,0" Radius="1.0"
                                                ThetaDiv="16" PhiDiv="16">
                                <helix:SphereVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF20B2AA"/>
                                        <SpecularMaterial Brush="#FF40E0D0" SpecularPower="150"/>
                                    </MaterialGroup>
                                </helix:SphereVisual3D.Material>
                            </helix:SphereVisual3D>

                            <!-- 支撑框架1 -->
                            <helix:BoxVisual3D x:Name="Frame1"
                                             Center="2,0.9,-1.5"
                                             Length="0.2" Width="0.2" Height="3">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFFFFF00"/>
                                        <SpecularMaterial Brush="#FFFFFF80" SpecularPower="100"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 支撑框架2 -->
                            <helix:BoxVisual3D x:Name="Frame2"
                                             Center="2,2.7,-1.5"
                                             Length="0.2" Width="0.2" Height="3">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FFFFFF00"/>
                                        <SpecularMaterial Brush="#FFFFFF80" SpecularPower="100"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                            <!-- 基座平台 -->
                            <helix:BoxVisual3D x:Name="BasePlatform"
                                             Center="0,1.8,-2.5"
                                             Length="8" Width="6" Height="0.3">
                                <helix:BoxVisual3D.Material>
                                    <MaterialGroup>
                                        <DiffuseMaterial Brush="#FF808080"/>
                                        <SpecularMaterial Brush="#FFA0A0A0" SpecularPower="80"/>
                                    </MaterialGroup>
                                </helix:BoxVisual3D.Material>
                            </helix:BoxVisual3D>

                        </helix:HelixViewport3D>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
        
        <!-- Bottom Status Bar -->
        <Border Grid.Row="2" BorderThickness="0,1,0,0" BorderBrush="{StaticResource BorderBrush}">
            <StatusBar Background="Transparent">
                <StatusBarItem>
                    <TextBlock Text="© 2025 政务大数据共享交换平台" Foreground="{StaticResource SecondaryTextBrush}" FontSize="11"/>
                </StatusBarItem>
            </StatusBar>
        </Border>
        
        <!-- Tab Panel at the Bottom of the Top Row (Above Lower Panel) -->
        <TabControl Grid.Row="1" Background="Transparent" BorderThickness="0" Margin="10,280,10,0"
                   VerticalAlignment="Top" Height="30">
            <TabItem Header="法人库" Foreground="White"/>
            <TabItem Header="人口库" Foreground="White"/>
            <TabItem Header="电子证照库" Foreground="White"/>
        </TabControl>
    </Grid>
</Window> 